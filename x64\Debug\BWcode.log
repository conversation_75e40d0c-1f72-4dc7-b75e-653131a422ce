﻿  Assembling include\syscalls_masm.asm...
  Dllmain.cpp
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
C:\Userfile\ccode\BWcode\BWcode\include\ntapi.hpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“Dllmain.cpp”)
  
C:\Userfile\ccode\BWcode\BWcode\include\ntapi.hpp(10,1): warning C4005: “STATUS_TIMEOUT”: 宏重定义
  (编译源文件“Dllmain.cpp”)
  C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winnt.h(2552,1):
  参见“STATUS_TIMEOUT”的前一个定义
  
C:\Userfile\ccode\BWcode\BWcode\include\ntapi.hpp(11,1): warning C4005: “STATUS_PENDING”: 宏重定义
  (编译源文件“Dllmain.cpp”)
  C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winnt.h(2553,1):
  参见“STATUS_PENDING”的前一个定义
  
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(164,9): error C2059: 语法错误:“return”
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(164,26): error C2238: 意外的标记位于“;”之前
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(167,1): error C2059: 语法错误:“private”
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(167,8): error C2059: 语法错误:“:”
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(125,23): error C3861: “GetKernel32Base”: 找不到标识符
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(131,46): error C3861: “GetProcAddressManual”: 找不到标识符
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(144,26): error C3861: “GetNtdllBaseInternal”: 找不到标识符
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(146,67): error C3861: “GetProcAddressManual”: 找不到标识符
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(147,65): error C3861: “GetProcAddressManual”: 找不到标识符
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(148,61): error C3861: “GetProcAddressManual”: 找不到标识符
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(149,49): error C3861: “GetProcAddressManual”: 找不到标识符
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(150,35): error C3861: “GetProcAddressManual”: 找不到标识符
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(151,69): error C3861: “GetProcAddressManual”: 找不到标识符
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(154,45): error C3861: “GetProcAddressManual”: 找不到标识符
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(155,47): error C3861: “GetProcAddressManual”: 找不到标识符
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(265,1): error C2059: 语法错误:“}”
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(265,1): error C2143: 语法错误: 缺少“;”(在“}”的前面)
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(278,9): error C2059: 语法错误:“if”
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(278,56): error C2238: 意外的标记位于“;”之前
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(282,9): error C2062: 意外的类型“void” 
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(282,9): error C2065: “format”: 未声明的标识符
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(282,9): error C3553: decltype 应为表达式而不是类型
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(282,31): error C2238: 意外的标记位于“;”之前
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(283,19): error C2061: 语法错误: 标识符“buffer”
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(283,56): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(283,56): warning C4183: “vsnprintf”: 缺少返回类型；假定为返回“int”的成员函数
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(284,9): error C2062: 意外的类型“void” 
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(284,21): error C2238: 意外的标记位于“;”之前
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(285,9): error C3927: "->": 非函数声明符后不允许尾随返回类型
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(285,22): error C3484: 语法错误: 返回类型前应为“->”
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(285,23): error C3613: “->”后缺少返回类型(假定为“int”)
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(285,23): error C3646: “fpOutputDebugStringA”: 未知重写说明符
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(285,43): error C2059: 语法错误:“(”
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(285,51): error C2238: 意外的标记位于“;”之前
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(289,27): error C2377: “LPVOID”: 重定义；typedef 不能由任何其他符号重载
  C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\minwindef.h(173,30):
  参见“LPVOID”的声明
  
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(289,12): error C2146: 语法错误: 缺少“;”(在标识符“EncodeSystemPtr”的前面)
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(289,35): error C2146: 语法错误: 缺少“)”(在标识符“ptr”的前面)
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(289,38): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(289,28): error C2371: “LPVOID”: 重定义；不同的基类型
  C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(289,5):
  参见“LPVOID”的声明
  
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(289,35): error C2146: 语法错误: 缺少“;”(在标识符“ptr”的前面)
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(289,38): error C2059: 语法错误:“)”
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(289,40): error C2143: 语法错误: 缺少“;”(在“{”的前面)
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(289,40): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(297,23): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(297,5): error C2371: “LPVOID”: 重定义；不同的基类型
  C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(289,5):
  参见“LPVOID”的声明
  
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(297,12): error C2146: 语法错误: 缺少“;”(在标识符“FindPattern”的前面)
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(297,92): error C2143: 语法错误: 缺少“;”(在“{”的前面)
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(297,92): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(309,35): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(309,5): error C2371: “LPVOID”: 重定义；不同的基类型
  C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(289,5):
  参见“LPVOID”的声明
  
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(309,12): error C2146: 语法错误: 缺少“;”(在标识符“FindSE_DllLoadedAddress”的前面)
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(309,76): error C2143: 语法错误: 缺少“;”(在“{”的前面)
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(309,76): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(366,35): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(366,5): error C2371: “LPVOID”: 重定义；不同的基类型
  C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(289,5):
  参见“LPVOID”的声明
  
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(366,12): error C2146: 语法错误: 缺少“;”(在标识符“FindShimsEnabledAddress”的前面)
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(366,83): error C2143: 语法错误: 缺少“;”(在“{”的前面)
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(366,83): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(424,1): error C2059: 语法错误:“public”
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(426,26): error C2059: 语法错误:“)”
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(426,39): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(426,61): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(426,68): error C2059: 语法错误:“{”
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(426,68): error C2143: 语法错误: 缺少“;”(在“{”的前面)
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(426,68): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(448,6): error C2588: “::~EarlyCascadeInjector”: 非法的全局 析构函数
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(448,29): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(449,9): error C3861: “Cleanup”: 找不到标识符
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(450,5): warning C4508: “EarlyCascadeInjector”: 函数应返回一个值；假定“void”返回类型
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(455,17): error C2065: “m_pi”: 未声明的标识符
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(456,17): error C2065: “m_syscalls”: 未声明的标识符
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(457,60): error C2065: “m_pi”: 未声明的标识符
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(458,17): error C2065: “m_pi”: 未声明的标识符
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(460,17): error C2065: “m_pi”: 未声明的标识符
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(461,17): error C2065: “m_syscalls”: 未声明的标识符
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(462,60): error C2065: “m_pi”: 未声明的标识符
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(463,17): error C2065: “m_pi”: 未声明的标识符
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(467,13): error C3861: “DebugOutput”: 找不到标识符
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(474,13): error C3861: “DebugOutput”: 找不到标识符
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(478,9): error C3861: “DebugOutput”: 找不到标识符
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(480,16): error C2146: 语法错误: 缺少“;”(在标识符“pBuffer”的前面)
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(480,16): error C2065: “pBuffer”: 未声明的标识符
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(481,16): error C2146: 语法错误: 缺少“;”(在标识符“pShimsEnabledAddress”的前面)
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(481,16): error C2065: “pShimsEnabledAddress”: 未声明的标识符
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(482,16): error C2146: 语法错误: 缺少“;”(在标识符“pSE_DllLoadedAddress”的前面)
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(482,16): error C2065: “pSE_DllLoadedAddress”: 未声明的标识符
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(483,16): error C2146: 语法错误: 缺少“;”(在标识符“pPtr”的前面)
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(483,16): error C2065: “pPtr”: 未声明的标识符
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(488,9): error C3861: “DebugOutput”: 找不到标识符
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(491,29): error C2065: “m_apiResolver”: 未声明的标识符
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(492,40): error C2065: “m_apiResolver”: 未声明的标识符
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(495,13): error C3861: “DebugOutput”: 找不到标识符
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(499,22): error C2143: 语法错误: 缺少“)”(在“__cdecl”的前面)
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(499,22): error C2143: 语法错误: 缺少“;”(在“__cdecl”的前面)
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(499,22): error C2059: 语法错误:“__cdecl”
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(499,45): error C2059: 语法错误:“)”
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(503,9): error C2065: “pCreateProcessA”: 未声明的标识符
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(503,25): error C2146: 语法错误: 缺少“;”(在标识符“fpCreateProcessA”的前面)
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(503,25): error C2065: “fpCreateProcessA”: 未声明的标识符
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(503,45): error C2065: “pCreateProcessA”: 未声明的标识符
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(504,13): error C2146: 语法错误: 缺少“;”(在标识符“pGetProcAddr”的前面)
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(506,14): error C2065: “fpCreateProcessA”: 未声明的标识符
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(507,13): error C3861: “DebugOutput”: 找不到标识符
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(516,44): error C2065: “m_si”: 未声明的标识符
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(516,51): error C2065: “m_pi”: 未声明的标识符
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(515,14): error C3861: “fpCreateProcessA”: 找不到标识符
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(518,13): error C2065: “pGetSystemDirectoryA”: 未声明的标识符
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(518,34): error C2146: 语法错误: 缺少“;”(在标识符“fpGetSystemDirectoryA”的前面)
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(518,34): error C2065: “fpGetSystemDirectoryA”: 未声明的标识符
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(518,59): error C2065: “pGetSystemDirectoryA”: 未声明的标识符
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(518,59): error C1003: 错误计数超过 100；正在停止编译
  obfuscation.cpp
  syscalls.cpp
  正在生成代码...
